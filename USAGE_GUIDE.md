# AutoBot - Verwendungsanleitung

## 🎯 Funktionsweise

Der AutoBot erkennt automatisch COD-Prozesse und kann dann den Bildschirm nach Texten wie "Call of Duty" und "Warzone" scannen und automatisch darauf klicken.

## 🚀 Schnellstart

### 1. AutoBot starten
```bash
python main.py
```

### 2. COD-Spiel starten
- Starten Sie Ihr Call of Duty Spiel (beliebige Version)
- Der AutoBot erkennt automatisch: cod.exe, warzone.exe, codmw.exe, etc.

### 3. Prozess-Monitor aktivieren
- Klicken Sie auf **"Monitor starten"**
- Status zeigt: "COD.exe: Gefunden (PID: XXXX)"

### 4. Text-Scanner verwenden
Nach erfolgreicher COD-Erkennung haben Sie folgende Optionen:

#### 🔍 **Text-Scanner starten** (<PERSON><PERSON><PERSON><PERSON><PERSON>)
- Scannt alle 3 Sekunden nach COD-Texten
- Klickt automatisch auf gefundene Texte
- Priorität: "Call of Duty" > "Warzone" > "Play" > etc.

#### 🎯 **Einmaliger Scan**
- Führt einen sofortigen Scan durch
- Zeigt alle gefundenen Texte im Log

#### 🎮 **Warzone suchen**
- Sucht speziell nach "Warzone"-Text
- Klickt sofort wenn gefunden

## 📊 GUI-Bereiche

### Status-Anzeige
- **COD.exe Status**: Zeigt ob Spiel erkannt wurde
- **Monitor Status**: Prozess-Überwachung aktiv/inaktiv
- **Text-Scanner Status**: Text-Scanning aktiv/inaktiv

### Kontrollen
- **Monitor starten/stoppen**: Prozess-Überwachung
- **Jetzt scannen**: Manuelle Prozess-Suche
- **Log löschen**: Aktivitätsprotokoll leeren

### Text-Scanner & Auto-Clicker
- **Text-Scanner starten/stoppen**: Kontinuierliche Text-Suche
- **Einmaliger Scan**: Sofortige Text-Suche
- **Warzone suchen**: Spezifische Warzone-Suche

### Aktivitätslog
- Detailliertes Protokoll aller Aktionen
- Emojis für bessere Übersicht:
  - 🎮 Spiel-Events
  - 🔍 Scan-Aktivitäten
  - ✅ Erfolgreiche Klicks
  - ❌ Fehler
  - ⚠️ Warnungen

## 🎯 Erkannte Texte

Der AutoBot erkennt folgende Texte und klickt darauf:

### Haupt-Ziele (Hohe Priorität)
- **"Call of Duty"** - Hauptmenü-Titel
- **"Warzone"** - Warzone-Modus
- **"Play"** - Spielen-Button
- **"Continue"** - Fortsetzen-Button

### Spiel-Modi (Mittlere Priorität)
- **"Multiplayer"** - Mehrspieler-Modus
- **"Campaign"** - Kampagne
- **"Zombies"** - Zombie-Modus

### Menü-Elemente (Niedrige Priorität)
- **"Battle Pass"** - Battle Pass
- **"Store"** - Shop
- **"Settings"** - Einstellungen

## ⚙️ Konfiguration

### Text-Scanner Einstellungen
Die folgenden Parameter können in `src/core/auto_clicker.py` angepasst werden:

```python
self.click_delay = 1.0          # Verzögerung zwischen Klicks (Sekunden)
self.scan_interval = 2.0        # Scan-Intervall (Sekunden)
self.max_attempts = 5           # Maximale Versuche pro Text
self.confidence_threshold = 0.6  # Mindest-Konfidenz für Klicks (0.0-1.0)
```

### Text-Prioritäten
```python
self.text_priorities = {
    "call of duty": 10,    # Höchste Priorität
    "warzone": 9,
    "play": 8,
    "continue": 8,
    "multiplayer": 6,
    "campaign": 5,
    "zombies": 5,
    "battle pass": 4,
    "store": 3,
    "settings": 2          # Niedrigste Priorität
}
```

## 🔧 Erweiterte Funktionen

### OCR-Engines
Der AutoBot verwendet zwei OCR-Engines für maximale Genauigkeit:
- **EasyOCR**: Moderne Deep Learning-basierte Texterkennung
- **Tesseract**: Klassische OCR-Engine als Backup

### Bildvorverarbeitung
Für bessere Texterkennung wird das Bild automatisch optimiert:
- Graustufen-Konvertierung
- Kontrast-Verbesserung
- Rauschreduzierung
- Binarisierung

### Fuzzy Matching
Erkennt auch ähnliche Texte mit Tippfehlern oder unterschiedlicher Formatierung.

## 🛡️ Sicherheitsfeatures

### PyAutoGUI Failsafe
- **Notfall-Stopp**: Bewegen Sie die Maus in die obere linke Ecke
- Stoppt sofort alle Automatisierung

### Prozess-Validierung
- Klicks nur wenn COD-Prozess aktiv ist
- Kontinuierliche Prozess-Überwachung

### Logging
- Alle Aktionen werden protokolliert
- Fehler-Behandlung und -Protokollierung

## 🎮 Typischer Workflow

1. **AutoBot starten**: `python main.py`
2. **COD-Spiel starten**: Beliebige COD-Version
3. **Monitor aktivieren**: "Monitor starten" klicken
4. **Warten auf Erkennung**: Status wird grün
5. **Text-Scanner starten**: "Text-Scanner starten" klicken
6. **Automatische Klicks**: Bot klickt auf erkannte Texte
7. **Überwachung**: Log zeigt alle Aktivitäten

## 🔍 Troubleshooting

### COD-Prozess nicht erkannt
- Stellen Sie sicher, dass das Spiel läuft
- Prüfen Sie den Prozessnamen im Task-Manager
- Fügen Sie neue Executable-Namen in `process_monitor.py` hinzu

### Texte nicht erkannt
- Prüfen Sie die Bildschirmauflösung
- Stellen Sie sicher, dass Texte sichtbar sind
- Reduzieren Sie `confidence_threshold` für mehr Treffer

### Klicks funktionieren nicht
- Prüfen Sie PyAutoGUI Failsafe (Maus-Position)
- Stellen Sie sicher, dass das Spiel im Vordergrund ist
- Erhöhen Sie `click_delay` für langsamere Systeme

### Performance-Probleme
- EasyOCR lädt beim ersten Start Modelle herunter
- Verwenden Sie GPU für bessere Performance
- Erhöhen Sie `scan_interval` für weniger CPU-Last

## 📝 Logs verstehen

### Log-Symbole
- 🎮 **Spiel-Events**: Prozess gefunden/verloren
- 🔍 **Scan-Events**: Text-Suche gestartet/beendet
- 👁️ **Text gefunden**: Text erkannt aber noch nicht geklickt
- 🖱️ **Klick ausgeführt**: Erfolgreicher Klick
- ✅ **Erfolg**: Aktion erfolgreich abgeschlossen
- ❌ **Fehler**: Fehler aufgetreten
- ⚠️ **Warnung**: Wichtige Information
- ℹ️ **Info**: Allgemeine Information
- 📊 **Statistik**: Scan-Ergebnisse

### Beispiel-Log
```
[11:28:15] 🎮 COD-Prozess gefunden: cod.exe (PID: 1234)
[11:28:20] 🔍 Text-Scanner gestartet - Suche nach COD-Texten...
[11:28:23] 👁️ Text gefunden: 'warzone' bei (1280, 720)
[11:28:23] 🖱️ Geklickt auf 'warzone' bei (1280, 720)
[11:28:23] ✅ Warzone gefunden und angeklickt!
```

## 🎯 Tipps für optimale Nutzung

1. **Vollbild-Modus**: Spiel im Vollbild für beste Erkennung
2. **Hohe Auflösung**: Bessere Texterkennung bei höherer Auflösung
3. **Stabile Verbindung**: Für EasyOCR-Modell-Download
4. **Geduld beim ersten Start**: Modelle werden heruntergeladen
5. **Log beobachten**: Für Debugging und Optimierung
