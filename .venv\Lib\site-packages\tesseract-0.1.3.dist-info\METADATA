Metadata-Version: 2.4
Name: tesseract
Version: 0.1.3
Summary: Tesselation based Recovery of Amorphous halo Concentrations
Home-page: http://vpac00.phy.vanderbilt.edu/~langmm/index.html
Author: <PERSON><PERSON> Lang
Author-email: <EMAIL>
Classifier: Programming Language :: Python
Classifier: Operating System :: OS Independent
Classifier: License :: OSI Approved :: GNU General Public License (GPL)
Classifier: Intended Audience :: Science/Research
Classifier: Natural Language :: English
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Astronomy
Classifier: Development Status :: 3 - Alpha
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: summary

The TesseRACt package is designed to compute concentrations of simulated dark
matter halos from volume info for particles generated using Voronoi tesselation.
This technique is advantageous as it is non-parametric, does not assume 
spherical symmetry, and allows for the presence of substructure. For a more
complete description of this technique including a comparison to other 
techniques for calculating concentration, please see the accompanying paper 
[<PERSON> et al. (2015)](http://arxiv.org/abs/1504.04307).

This package includes:

 * **vorovol**: C program for computing the Voronoi diagram of particle data in 
    a number of formats including Gadget-2, Gasoline, binary, and ASCII as well
    as BGC2 halo catalogues.
 * routines for compiling, running, and parsing **vorovol** output
 * routines for computing concentrations using particles volumes, traditional 
    fitting to an NFW profile, and non-parametric techniques that assume 
    spherical symmetry.
 * routines and test halos for running many of the performance tests presented in 
    [Lang et al. (2015)](http://arxiv.org/abs/1504.04307).

Below are some useful links associated with TesseRACt:
 * [PyPI](https://pypi.python.org/pypi/tesseract) - The most recent stable release.
 * [Docs](https://readthedocs.org/projects/pytesseract/) - Tutorials and descriptions of the package modules and functions.
 * [Lang et al. (2015)](http://arxiv.org/abs/1504.04307) - The accompanying scientific paper.

If you would like more information about TesseRACt, please contact [Meagan Lang](mailto:<EMAIL>).
