"""
Text-Scanner für OCR-basierte Texterkennung auf dem Bildschirm
"""

import cv2
import numpy as np
import easyocr
import pytesseract
import re
from typing import List, Dict, Tuple, Optional
from PIL import Image, ImageGrab
from ..utils.logger import Logger

class TextScanner:
    """Klasse für OCR-basierte Texterkennung auf dem Bildschirm"""
    
    def __init__(self):
        self.logger = Logger()
        
        # EasyOCR Reader initialisieren (Deutsch und Englisch)
        try:
            self.easyocr_reader = easyocr.Reader(['en', 'de'], gpu=False)
            self.logger.info("EasyOCR Reader erfolgreich initialisiert")
        except Exception as e:
            self.logger.warning(f"EasyOCR konnte nicht initialisiert werden: {e}")
            self.easyocr_reader = None
        
        # Suchbegriffe für COD-Spiele
        self.target_texts = [
            "call of duty",
            "warzone", 
            "modern warfare",
            "black ops",
            "vanguard",
            "cold war",
            "cod",
            "battle pass",
            "multiplayer",
            "campaign",
            "zombies"
        ]
        
        # Zusätzliche Varianten und Schreibweisen
        self.text_variations = {
            "call of duty": ["call of duty", "callofduty", "call duty", "cod"],
            "warzone": ["warzone", "war zone", "wz"],
            "modern warfare": ["modern warfare", "modernwarfare", "mw", "mw2", "mw3"],
            "black ops": ["black ops", "blackops", "bo", "bo2", "bo3", "bo4"],
            "vanguard": ["vanguard", "cod vanguard"],
            "cold war": ["cold war", "coldwar", "cw"],
            "battle pass": ["battle pass", "battlepass", "bp"],
            "multiplayer": ["multiplayer", "multi", "mp"],
            "campaign": ["campaign", "story", "single"],
            "zombies": ["zombies", "zombie", "undead"]
        }
    
    def take_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        Macht einen Screenshot und konvertiert ihn für OCR
        
        Args:
            region: Optional (x, y, width, height) für Teilbereich
            
        Returns:
            np.ndarray: Screenshot als OpenCV-Array
        """
        try:
            if region:
                screenshot = ImageGrab.grab(bbox=region)
            else:
                screenshot = ImageGrab.grab()
            
            # PIL zu OpenCV konvertieren
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return screenshot_cv
            
        except Exception as e:
            self.logger.error(f"Fehler beim Screenshot: {e}")
            raise
    
    def preprocess_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """
        Bereitet das Bild für bessere OCR-Ergebnisse vor
        
        Args:
            image: OpenCV-Bild
            
        Returns:
            np.ndarray: Vorverarbeitetes Bild
        """
        try:
            # Zu Graustufen konvertieren
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Kontrast erhöhen
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Rauschen reduzieren
            denoised = cv2.medianBlur(enhanced, 3)
            
            # Binarisierung für bessere Texterkennung
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            return binary
            
        except Exception as e:
            self.logger.error(f"Fehler bei Bildvorverarbeitung: {e}")
            return image
    
    def extract_text_easyocr(self, image: np.ndarray) -> List[Dict[str, any]]:
        """
        Extrahiert Text mit EasyOCR
        
        Args:
            image: OpenCV-Bild
            
        Returns:
            List[Dict]: Liste der erkannten Texte mit Positionen
        """
        if not self.easyocr_reader:
            return []
        
        try:
            # EasyOCR ausführen
            results = self.easyocr_reader.readtext(image)
            
            extracted_texts = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Mindestvertrauen
                    # Bounding Box Koordinaten extrahieren
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    
                    x_min, x_max = int(min(x_coords)), int(max(x_coords))
                    y_min, y_max = int(min(y_coords)), int(max(y_coords))
                    
                    center_x = (x_min + x_max) // 2
                    center_y = (y_min + y_max) // 2
                    
                    extracted_texts.append({
                        'text': text.strip(),
                        'confidence': confidence,
                        'bbox': (x_min, y_min, x_max, y_max),
                        'center': (center_x, center_y),
                        'method': 'easyocr'
                    })
            
            return extracted_texts
            
        except Exception as e:
            self.logger.error(f"Fehler bei EasyOCR: {e}")
            return []
    
    def extract_text_tesseract(self, image: np.ndarray) -> List[Dict[str, any]]:
        """
        Extrahiert Text mit Tesseract OCR

        Args:
            image: OpenCV-Bild

        Returns:
            List[Dict]: Liste der erkannten Texte mit Positionen
        """
        try:
            # Prüfen ob Tesseract verfügbar ist
            import shutil
            if not shutil.which('tesseract'):
                self.logger.warning("Tesseract nicht installiert - verwende nur EasyOCR")
                return []

            # Tesseract Konfiguration
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 '

            # Text mit Bounding Boxes extrahieren
            data = pytesseract.image_to_data(image, config=config, output_type=pytesseract.Output.DICT)

            extracted_texts = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])

                if text and confidence > 30:  # Mindestvertrauen
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]

                    center_x = x + w // 2
                    center_y = y + h // 2

                    extracted_texts.append({
                        'text': text,
                        'confidence': confidence / 100.0,  # Normalisieren auf 0-1
                        'bbox': (x, y, x + w, y + h),
                        'center': (center_x, center_y),
                        'method': 'tesseract'
                    })

            return extracted_texts

        except Exception as e:
            self.logger.warning(f"Tesseract nicht verfügbar: {e}")
            return []
    
    def find_target_texts(self, region: Optional[Tuple[int, int, int, int]] = None) -> List[Dict[str, any]]:
        """
        Sucht nach Ziel-Texten auf dem Bildschirm
        
        Args:
            region: Optional Suchbereich (x, y, width, height)
            
        Returns:
            List[Dict]: Gefundene Ziel-Texte mit Positionen
        """
        try:
            # Screenshot machen
            screenshot = self.take_screenshot(region)
            
            # Bild für OCR vorbereiten
            processed_image = self.preprocess_image_for_ocr(screenshot)
            
            # Text mit beiden Methoden extrahieren
            all_texts = []
            
            # EasyOCR
            easyocr_texts = self.extract_text_easyocr(screenshot)
            all_texts.extend(easyocr_texts)
            
            # Tesseract
            tesseract_texts = self.extract_text_tesseract(processed_image)
            all_texts.extend(tesseract_texts)
            
            # Nach Ziel-Texten filtern
            found_targets = []
            
            for text_data in all_texts:
                text = text_data['text'].lower()
                
                # Prüfen ob Text einem Ziel entspricht
                for target in self.target_texts:
                    if self._text_matches_target(text, target):
                        # Position anpassen wenn region verwendet wurde
                        center_x, center_y = text_data['center']
                        if region:
                            center_x += region[0]
                            center_y += region[1]
                        
                        found_targets.append({
                            'target': target,
                            'found_text': text_data['text'],
                            'confidence': text_data['confidence'],
                            'position': (center_x, center_y),
                            'bbox': text_data['bbox'],
                            'method': text_data['method']
                        })
                        
                        self.logger.info(f"Ziel-Text gefunden: '{target}' als '{text_data['text']}' bei ({center_x}, {center_y})")
            
            return found_targets
            
        except Exception as e:
            self.logger.error(f"Fehler bei Textsuche: {e}")
            return []
    
    def _text_matches_target(self, found_text: str, target: str) -> bool:
        """
        Prüft ob ein gefundener Text einem Ziel entspricht
        
        Args:
            found_text: Gefundener Text (lowercase)
            target: Ziel-Text
            
        Returns:
            bool: True wenn übereinstimmend
        """
        # Exakte Übereinstimmung
        if target in found_text:
            return True
        
        # Varianten prüfen
        if target in self.text_variations:
            for variation in self.text_variations[target]:
                if variation.lower() in found_text:
                    return True
        
        # Fuzzy Matching für ähnliche Texte
        if self._fuzzy_match(found_text, target):
            return True
        
        return False
    
    def _fuzzy_match(self, text1: str, text2: str, threshold: float = 0.8) -> bool:
        """
        Fuzzy String Matching
        
        Args:
            text1, text2: Zu vergleichende Texte
            threshold: Ähnlichkeitsschwelle
            
        Returns:
            bool: True wenn ähnlich genug
        """
        try:
            from difflib import SequenceMatcher
            similarity = SequenceMatcher(None, text1, text2).ratio()
            return similarity >= threshold
        except:
            return False
    
    def find_cod_menu_elements(self, region: Optional[Tuple[int, int, int, int]] = None) -> List[Dict[str, any]]:
        """
        Speziell für COD-Menü-Elemente optimierte Suche
        
        Args:
            region: Optional Suchbereich
            
        Returns:
            List[Dict]: Gefundene COD-Menü-Elemente
        """
        # Erweiterte Suchbegriffe für COD-Menüs
        menu_targets = [
            "call of duty",
            "warzone", 
            "multiplayer",
            "campaign",
            "zombies",
            "battle pass",
            "store",
            "settings",
            "play",
            "continue",
            "start",
            "join",
            "create",
            "lobby"
        ]
        
        # Temporär die Ziel-Texte erweitern
        original_targets = self.target_texts.copy()
        self.target_texts = menu_targets
        
        try:
            results = self.find_target_texts(region)
            return results
        finally:
            # Original-Ziele wiederherstellen
            self.target_texts = original_targets
    
    def scan_for_clickable_text(self, target_text: str, region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """
        Sucht nach einem spezifischen klickbaren Text
        
        Args:
            target_text: Zu suchender Text
            region: Optional Suchbereich
            
        Returns:
            Optional[Tuple[int, int]]: Position zum Klicken oder None
        """
        try:
            # Temporär nur nach diesem Text suchen
            original_targets = self.target_texts.copy()
            self.target_texts = [target_text.lower()]
            
            results = self.find_target_texts(region)
            
            if results:
                # Bestes Ergebnis zurückgeben (höchste Konfidenz)
                best_result = max(results, key=lambda x: x['confidence'])
                return best_result['position']
            
            return None
            
        finally:
            # Original-Ziele wiederherstellen
            self.target_texts = original_targets
