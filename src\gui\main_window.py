"""
AutoBot Hauptfenster - CustomTkinter GUI
"""

import customtkinter as ctk
import threading
import time
from typing import Optional

from ..core.process_monitor import ProcessMonitor
from ..core.auto_clicker import AutoClicker
from ..utils.logger import Logger

# CustomTkinter Erscheinungsbild konfigurieren
ctk.set_appearance_mode("dark")  # "dark" oder "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class AutoBotApp:
    """Hauptanwendungsklasse für den AutoBot"""
    
    def __init__(self):
        self.logger = Logger()
        self.process_monitor = ProcessMonitor()
        self.auto_clicker = AutoClicker()
        self.monitoring_active = False
        self.text_scanning_active = False
        self.monitoring_thread: Optional[threading.Thread] = None
        self.text_scanning_thread: Optional[threading.Thread] = None

        # Auto-Clicker Callbacks setzen
        self.auto_clicker.set_callbacks(
            on_text_found=self._on_text_found,
            on_click_performed=self._on_click_performed,
            on_scan_complete=self._on_scan_complete
        )
        
        # Hauptfenster erstellen
        self.root = ctk.CTk()
        self.root.title("AutoBot - COD Process & Text Scanner")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # GUI-Komponenten initialisieren
        self._setup_gui()
        
        # Event-Handler für Fenster schließen
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _setup_gui(self):
        """GUI-Layout erstellen"""
        
        # Hauptcontainer
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Titel
        title_label = ctk.CTkLabel(
            main_frame,
            text="AutoBot - COD Process & Text Scanner",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Status-Bereich
        self._setup_status_section(main_frame)

        # Kontroll-Bereich
        self._setup_control_section(main_frame)

        # Text-Scanner-Bereich
        self._setup_text_scanner_section(main_frame)

        # Log-Bereich
        self._setup_log_section(main_frame)
    
    def _setup_status_section(self, parent):
        """Status-Bereich erstellen"""
        status_frame = ctk.CTkFrame(parent)
        status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Status-Label
        status_title = ctk.CTkLabel(
            status_frame,
            text="Game Status",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 5))
        
        # COD Status
        self.cod_status_label = ctk.CTkLabel(
            status_frame,
            text="COD.exe: Nicht gefunden",
            font=ctk.CTkFont(size=14)
        )
        self.cod_status_label.pack(pady=5)
        
        # Monitor Status
        self.monitor_status_label = ctk.CTkLabel(
            status_frame,
            text="Monitor: Gestoppt",
            font=ctk.CTkFont(size=14)
        )
        self.monitor_status_label.pack(pady=5)

        # Text-Scanner Status
        self.text_scanner_status_label = ctk.CTkLabel(
            status_frame,
            text="Text-Scanner: Gestoppt",
            font=ctk.CTkFont(size=14)
        )
        self.text_scanner_status_label.pack(pady=(5, 15))
    
    def _setup_control_section(self, parent):
        """Kontroll-Bereich erstellen"""
        control_frame = ctk.CTkFrame(parent)
        control_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Kontroll-Titel
        control_title = ctk.CTkLabel(
            control_frame,
            text="Kontrollen",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.pack(pady=(15, 10))
        
        # Button-Container
        button_frame = ctk.CTkFrame(control_frame)
        button_frame.pack(pady=(0, 15))
        
        # Start/Stop Monitor Button
        self.monitor_button = ctk.CTkButton(
            button_frame,
            text="Monitor starten",
            command=self._toggle_monitoring,
            width=150,
            height=40
        )
        self.monitor_button.pack(side="left", padx=10, pady=15)
        
        # Manueller Scan Button
        self.scan_button = ctk.CTkButton(
            button_frame,
            text="Jetzt scannen",
            command=self._manual_scan,
            width=150,
            height=40
        )
        self.scan_button.pack(side="left", padx=10, pady=15)
        
        # Log löschen Button
        self.clear_log_button = ctk.CTkButton(
            button_frame,
            text="Log löschen",
            command=self._clear_log,
            width=150,
            height=40
        )
        self.clear_log_button.pack(side="left", padx=10, pady=15)

    def _setup_text_scanner_section(self, parent):
        """Text-Scanner-Bereich erstellen"""
        scanner_frame = ctk.CTkFrame(parent)
        scanner_frame.pack(fill="x", padx=20, pady=(0, 20))

        # Scanner-Titel
        scanner_title = ctk.CTkLabel(
            scanner_frame,
            text="Text-Scanner & Auto-Clicker",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        scanner_title.pack(pady=(15, 10))

        # Beschreibung
        description_label = ctk.CTkLabel(
            scanner_frame,
            text="Scannt nach 'Call of Duty', 'Warzone' und anderen COD-Texten und klickt automatisch darauf",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        description_label.pack(pady=(0, 10))

        # Button-Container für Scanner
        scanner_button_frame = ctk.CTkFrame(scanner_frame)
        scanner_button_frame.pack(pady=(0, 15))

        # Text-Scanner Start/Stop Button
        self.text_scanner_button = ctk.CTkButton(
            scanner_button_frame,
            text="Text-Scanner starten",
            command=self._toggle_text_scanning,
            width=180,
            height=40,
            fg_color="green"
        )
        self.text_scanner_button.pack(side="left", padx=10, pady=15)

        # Einmaliger Scan Button
        self.single_scan_button = ctk.CTkButton(
            scanner_button_frame,
            text="Einmaliger Scan",
            command=self._perform_single_scan,
            width=150,
            height=40
        )
        self.single_scan_button.pack(side="left", padx=10, pady=15)

        # Warzone-spezifischer Button
        self.warzone_scan_button = ctk.CTkButton(
            scanner_button_frame,
            text="Warzone suchen",
            command=self._scan_for_warzone,
            width=150,
            height=40,
            fg_color="orange"
        )
        self.warzone_scan_button.pack(side="left", padx=10, pady=15)
    
    def _setup_log_section(self, parent):
        """Log-Bereich erstellen"""
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Log-Titel
        log_title = ctk.CTkLabel(
            log_frame,
            text="Aktivitätslog",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_title.pack(pady=(15, 10))
        
        # Log-Textbereich
        self.log_textbox = ctk.CTkTextbox(
            log_frame,
            width=750,
            height=200,
            font=ctk.CTkFont(family="Consolas", size=12)
        )
        self.log_textbox.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # Erste Log-Nachricht
        self._add_log_message("AutoBot gestartet. Bereit zum Überwachen von COD-Prozessen.")
    
    def _toggle_monitoring(self):
        """Monitoring ein-/ausschalten"""
        if not self.monitoring_active:
            self._start_monitoring()
        else:
            self._stop_monitoring()
    
    def _start_monitoring(self):
        """Monitoring starten"""
        self.monitoring_active = True
        self.monitor_button.configure(text="Monitor stoppen")
        self.monitor_status_label.configure(text="Monitor: Aktiv")
        
        # Monitoring-Thread starten
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self._add_log_message("Prozess-Monitoring gestartet...")
    
    def _stop_monitoring(self):
        """Monitoring stoppen"""
        self.monitoring_active = False
        self.monitor_button.configure(text="Monitor starten")
        self.monitor_status_label.configure(text="Monitor: Gestoppt")
        
        self._add_log_message("Prozess-Monitoring gestoppt.")

    def _toggle_text_scanning(self):
        """Text-Scanning ein-/ausschalten"""
        if not self.text_scanning_active:
            self._start_text_scanning()
        else:
            self._stop_text_scanning()

    def _start_text_scanning(self):
        """Text-Scanning starten"""
        # Prüfen ob COD-Prozess läuft
        cod_processes = self.process_monitor.find_cod_processes()
        if not cod_processes:
            self._add_log_message("⚠️ Kein COD-Prozess gefunden! Bitte starten Sie zuerst das Spiel.")
            return

        self.text_scanning_active = True
        self.text_scanner_button.configure(text="Text-Scanner stoppen", fg_color="red")
        self.text_scanner_status_label.configure(text="Text-Scanner: Aktiv", text_color="green")

        # Text-Scanning-Thread starten
        self.text_scanning_thread = threading.Thread(target=self._text_scanning_loop, daemon=True)
        self.text_scanning_thread.start()

        self._add_log_message("🔍 Text-Scanner gestartet - Suche nach COD-Texten...")

    def _stop_text_scanning(self):
        """Text-Scanning stoppen"""
        self.text_scanning_active = False
        self.text_scanner_button.configure(text="Text-Scanner starten", fg_color="green")
        self.text_scanner_status_label.configure(text="Text-Scanner: Gestoppt", text_color="red")

        self._add_log_message("🔍 Text-Scanner gestoppt.")

    def _text_scanning_loop(self):
        """Haupt-Text-Scanning-Schleife"""
        while self.text_scanning_active:
            try:
                # Nach COD-Texten scannen und klicken
                clicked_elements = self.auto_clicker.scan_and_click_cod_elements()

                if clicked_elements:
                    for element in clicked_elements:
                        self.root.after(0, self._add_log_message,
                                      f"✅ Geklickt auf '{element['target']}' bei ({element['position'][0]}, {element['position'][1]})")

                # 3 Sekunden warten vor nächstem Scan
                time.sleep(3)

            except Exception as e:
                self.logger.error(f"Fehler im Text-Scanning-Loop: {e}")
                self.root.after(0, self._add_log_message, f"❌ Text-Scanning-Fehler: {e}")
                time.sleep(5)

    def _perform_single_scan(self):
        """Führt einen einmaligen Text-Scan durch"""
        self._add_log_message("🔍 Starte einmaligen Text-Scan...")

        try:
            # Nach COD-Texten scannen
            clicked_elements = self.auto_clicker.scan_and_click_cod_elements()

            if clicked_elements:
                for element in clicked_elements:
                    self._add_log_message(
                        f"✅ Gefunden und geklickt: '{element['target']}' "
                        f"(Konfidenz: {element['confidence']:.2f}) "
                        f"bei ({element['position'][0]}, {element['position'][1]})"
                    )
            else:
                self._add_log_message("ℹ️ Keine klickbaren COD-Texte gefunden.")

        except Exception as e:
            self.logger.error(f"Fehler beim einmaligen Scan: {e}")
            self._add_log_message(f"❌ Scan-Fehler: {e}")

    def _scan_for_warzone(self):
        """Sucht speziell nach Warzone-Text"""
        self._add_log_message("🎯 Suche speziell nach 'Warzone'...")

        try:
            success = self.auto_clicker.click_on_text("warzone")

            if success:
                self._add_log_message("✅ Warzone gefunden und angeklickt!")
            else:
                self._add_log_message("❌ Warzone-Text nicht gefunden.")

        except Exception as e:
            self.logger.error(f"Fehler bei Warzone-Suche: {e}")
            self._add_log_message(f"❌ Warzone-Suche-Fehler: {e}")

    def _on_text_found(self, text: str, position: tuple):
        """Callback wenn Text gefunden wird"""
        self._add_log_message(f"👁️ Text gefunden: '{text}' bei ({position[0]}, {position[1]})")

    def _on_click_performed(self, text: str, position: tuple):
        """Callback wenn Klick ausgeführt wird"""
        self._add_log_message(f"🖱️ Geklickt auf '{text}' bei ({position[0]}, {position[1]})")

    def _on_scan_complete(self, found_elements: list, clicked_elements: list):
        """Callback wenn Scan abgeschlossen ist"""
        if found_elements:
            self._add_log_message(f"📊 Scan abgeschlossen: {len(found_elements)} Texte gefunden, {len(clicked_elements)} geklickt")
    
    def _monitoring_loop(self):
        """Haupt-Monitoring-Schleife"""
        while self.monitoring_active:
            try:
                cod_processes = self.process_monitor.find_cod_processes()
                
                # UI im Hauptthread aktualisieren
                self.root.after(0, self._update_cod_status, cod_processes)
                
                # 2 Sekunden warten vor nächstem Scan
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"Fehler im Monitoring-Loop: {e}")
                self.root.after(0, self._add_log_message, f"Monitoring-Fehler: {e}")
    
    def _update_cod_status(self, cod_processes):
        """COD-Status in der GUI aktualisieren"""
        if cod_processes:
            process_info = cod_processes[0]  # Ersten gefundenen Prozess nehmen
            status_text = f"COD.exe: Gefunden (PID: {process_info['pid']})"
            self.cod_status_label.configure(text=status_text, text_color="green")
            
            # Log-Nachricht nur beim ersten Fund
            if not hasattr(self, '_cod_found_logged') or not self._cod_found_logged:
                self._add_log_message(f"COD-Prozess gefunden: {process_info['name']} (PID: {process_info['pid']})")
                self._cod_found_logged = True
        else:
            self.cod_status_label.configure(text="COD.exe: Nicht gefunden", text_color="red")
            
            # Reset des Log-Flags
            if hasattr(self, '_cod_found_logged'):
                self._cod_found_logged = False
    
    def _manual_scan(self):
        """Manuellen Scan durchführen"""
        self._add_log_message("Manueller Scan gestartet...")
        
        try:
            cod_processes = self.process_monitor.find_cod_processes()
            
            if cod_processes:
                for process in cod_processes:
                    self._add_log_message(
                        f"Gefunden: {process['name']} (PID: {process['pid']}, "
                        f"Pfad: {process['exe_path']})"
                    )
            else:
                self._add_log_message("Keine COD-Prozesse gefunden.")
                
            # Status aktualisieren
            self._update_cod_status(cod_processes)
            
        except Exception as e:
            self.logger.error(f"Fehler beim manuellen Scan: {e}")
            self._add_log_message(f"Scan-Fehler: {e}")
    
    def _clear_log(self):
        """Log-Bereich leeren"""
        self.log_textbox.delete("1.0", "end")
        self._add_log_message("Log geleert.")
    
    def _add_log_message(self, message: str):
        """Nachricht zum Log hinzufügen"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_textbox.insert("end", log_entry)
        self.log_textbox.see("end")  # Automatisch zum Ende scrollen
    
    def _on_closing(self):
        """Beim Schließen des Fensters"""
        if self.monitoring_active:
            self._stop_monitoring()

        if self.text_scanning_active:
            self._stop_text_scanning()

        self.logger.info("AutoBot wird beendet...")
        self.root.destroy()
    
    def run(self):
        """Anwendung starten"""
        self.logger.info("AutoBot GUI gestartet")
        self._add_log_message("🎮 Bitte starten Sie Ihr COD-Spiel und aktivieren Sie den Monitor")
        self._add_log_message("🔍 Nach COD-Erkennung können Sie den Text-Scanner für automatische Klicks verwenden")
        self.root.mainloop()
