"""
AutoBot Hauptfenster - CustomTkinter GUI
"""

import customtkinter as ctk
import threading
import time
from typing import Optional

from ..core.process_monitor import ProcessMonitor
from ..utils.logger import Logger

# CustomTkinter Erscheinungsbild konfigurieren
ctk.set_appearance_mode("dark")  # "dark" oder "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

class AutoBotApp:
    """Hauptanwendungsklasse für den AutoBot"""
    
    def __init__(self):
        self.logger = Logger()
        self.process_monitor = ProcessMonitor()
        self.monitoring_active = False
        self.monitoring_thread: Optional[threading.Thread] = None
        
        # Hauptfenster erstellen
        self.root = ctk.CTk()
        self.root.title("AutoBot - COD Process Monitor")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # GUI-Komponenten initialisieren
        self._setup_gui()
        
        # Event-Handler für Fenster schließen
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _setup_gui(self):
        """GUI-Layout erstellen"""
        
        # Hauptcontainer
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Titel
        title_label = ctk.CTkLabel(
            main_frame, 
            text="AutoBot - COD Process Monitor",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Status-Bereich
        self._setup_status_section(main_frame)
        
        # Kontroll-Bereich
        self._setup_control_section(main_frame)
        
        # Log-Bereich
        self._setup_log_section(main_frame)
    
    def _setup_status_section(self, parent):
        """Status-Bereich erstellen"""
        status_frame = ctk.CTkFrame(parent)
        status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Status-Label
        status_title = ctk.CTkLabel(
            status_frame,
            text="Game Status",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 5))
        
        # COD Status
        self.cod_status_label = ctk.CTkLabel(
            status_frame,
            text="COD.exe: Nicht gefunden",
            font=ctk.CTkFont(size=14)
        )
        self.cod_status_label.pack(pady=5)
        
        # Monitor Status
        self.monitor_status_label = ctk.CTkLabel(
            status_frame,
            text="Monitor: Gestoppt",
            font=ctk.CTkFont(size=14)
        )
        self.monitor_status_label.pack(pady=(5, 15))
    
    def _setup_control_section(self, parent):
        """Kontroll-Bereich erstellen"""
        control_frame = ctk.CTkFrame(parent)
        control_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Kontroll-Titel
        control_title = ctk.CTkLabel(
            control_frame,
            text="Kontrollen",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.pack(pady=(15, 10))
        
        # Button-Container
        button_frame = ctk.CTkFrame(control_frame)
        button_frame.pack(pady=(0, 15))
        
        # Start/Stop Monitor Button
        self.monitor_button = ctk.CTkButton(
            button_frame,
            text="Monitor starten",
            command=self._toggle_monitoring,
            width=150,
            height=40
        )
        self.monitor_button.pack(side="left", padx=10, pady=15)
        
        # Manueller Scan Button
        self.scan_button = ctk.CTkButton(
            button_frame,
            text="Jetzt scannen",
            command=self._manual_scan,
            width=150,
            height=40
        )
        self.scan_button.pack(side="left", padx=10, pady=15)
        
        # Log löschen Button
        self.clear_log_button = ctk.CTkButton(
            button_frame,
            text="Log löschen",
            command=self._clear_log,
            width=150,
            height=40
        )
        self.clear_log_button.pack(side="left", padx=10, pady=15)
    
    def _setup_log_section(self, parent):
        """Log-Bereich erstellen"""
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Log-Titel
        log_title = ctk.CTkLabel(
            log_frame,
            text="Aktivitätslog",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_title.pack(pady=(15, 10))
        
        # Log-Textbereich
        self.log_textbox = ctk.CTkTextbox(
            log_frame,
            width=750,
            height=200,
            font=ctk.CTkFont(family="Consolas", size=12)
        )
        self.log_textbox.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # Erste Log-Nachricht
        self._add_log_message("AutoBot gestartet. Bereit zum Überwachen von COD-Prozessen.")
    
    def _toggle_monitoring(self):
        """Monitoring ein-/ausschalten"""
        if not self.monitoring_active:
            self._start_monitoring()
        else:
            self._stop_monitoring()
    
    def _start_monitoring(self):
        """Monitoring starten"""
        self.monitoring_active = True
        self.monitor_button.configure(text="Monitor stoppen")
        self.monitor_status_label.configure(text="Monitor: Aktiv")
        
        # Monitoring-Thread starten
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self._add_log_message("Prozess-Monitoring gestartet...")
    
    def _stop_monitoring(self):
        """Monitoring stoppen"""
        self.monitoring_active = False
        self.monitor_button.configure(text="Monitor starten")
        self.monitor_status_label.configure(text="Monitor: Gestoppt")
        
        self._add_log_message("Prozess-Monitoring gestoppt.")
    
    def _monitoring_loop(self):
        """Haupt-Monitoring-Schleife"""
        while self.monitoring_active:
            try:
                cod_processes = self.process_monitor.find_cod_processes()
                
                # UI im Hauptthread aktualisieren
                self.root.after(0, self._update_cod_status, cod_processes)
                
                # 2 Sekunden warten vor nächstem Scan
                time.sleep(2)
                
            except Exception as e:
                self.logger.error(f"Fehler im Monitoring-Loop: {e}")
                self.root.after(0, self._add_log_message, f"Monitoring-Fehler: {e}")
    
    def _update_cod_status(self, cod_processes):
        """COD-Status in der GUI aktualisieren"""
        if cod_processes:
            process_info = cod_processes[0]  # Ersten gefundenen Prozess nehmen
            status_text = f"COD.exe: Gefunden (PID: {process_info['pid']})"
            self.cod_status_label.configure(text=status_text, text_color="green")
            
            # Log-Nachricht nur beim ersten Fund
            if not hasattr(self, '_cod_found_logged') or not self._cod_found_logged:
                self._add_log_message(f"COD-Prozess gefunden: {process_info['name']} (PID: {process_info['pid']})")
                self._cod_found_logged = True
        else:
            self.cod_status_label.configure(text="COD.exe: Nicht gefunden", text_color="red")
            
            # Reset des Log-Flags
            if hasattr(self, '_cod_found_logged'):
                self._cod_found_logged = False
    
    def _manual_scan(self):
        """Manuellen Scan durchführen"""
        self._add_log_message("Manueller Scan gestartet...")
        
        try:
            cod_processes = self.process_monitor.find_cod_processes()
            
            if cod_processes:
                for process in cod_processes:
                    self._add_log_message(
                        f"Gefunden: {process['name']} (PID: {process['pid']}, "
                        f"Pfad: {process['exe_path']})"
                    )
            else:
                self._add_log_message("Keine COD-Prozesse gefunden.")
                
            # Status aktualisieren
            self._update_cod_status(cod_processes)
            
        except Exception as e:
            self.logger.error(f"Fehler beim manuellen Scan: {e}")
            self._add_log_message(f"Scan-Fehler: {e}")
    
    def _clear_log(self):
        """Log-Bereich leeren"""
        self.log_textbox.delete("1.0", "end")
        self._add_log_message("Log geleert.")
    
    def _add_log_message(self, message: str):
        """Nachricht zum Log hinzufügen"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_textbox.insert("end", log_entry)
        self.log_textbox.see("end")  # Automatisch zum Ende scrollen
    
    def _on_closing(self):
        """Beim Schließen des Fensters"""
        if self.monitoring_active:
            self._stop_monitoring()
        
        self.logger.info("AutoBot wird beendet...")
        self.root.destroy()
    
    def run(self):
        """Anwendung starten"""
        self.logger.info("AutoBot GUI gestartet")
        self._add_log_message("Bitte starten Sie Ihr COD-Spiel und klicken Sie auf 'Monitor starten'")
        self.root.mainloop()
