"""
Prozess-Monitor für das Erkennen von COD-Spielprozessen
"""

import psutil
import os
from typing import List, Dict, Optional
from ..utils.logger import Logger

class ProcessMonitor:
    """Klasse zum Überwachen und Erkennen von COD-Prozessen"""
    
    def __init__(self):
        self.logger = Logger()
        
        # Liste der bekannten COD-Executable-Namen
        self.cod_executables = [
            "cod.exe",
            "codmw.exe", 
            "codmw2.exe",
            "codmw3.exe",
            "codbo.exe",
            "codbo2.exe",
            "codbo3.exe",
            "codbo4.exe",
            "codcw.exe",  # Cold War
            "codvanguard.exe",
            "codwarzone.exe",
            "modernwarfare.exe",
            "blackops.exe",
            "warzone.exe",
            "callofduty.exe"
        ]
    
    def find_cod_processes(self) -> List[Dict[str, any]]:
        """
        Sucht nach laufenden COD-Prozessen
        
        Returns:
            List[Dict]: Liste der gefundenen COD-Prozesse mit Informationen
        """
        found_processes = []
        
        try:
            # Alle laufenden Prozesse durchsuchen
            for process in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
                try:
                    process_info = process.info
                    process_name = process_info['name'].lower() if process_info['name'] else ""
                    
                    # Prüfen ob der Prozessname einem COD-Executable entspricht
                    if self._is_cod_process(process_name):
                        cod_process_info = {
                            'pid': process_info['pid'],
                            'name': process_info['name'],
                            'exe_path': process_info['exe'] or "Unbekannt",
                            'create_time': process_info['create_time'],
                            'process_obj': process  # Für weitere Operationen
                        }
                        
                        found_processes.append(cod_process_info)
                        self.logger.info(f"COD-Prozess gefunden: {process_info['name']} (PID: {process_info['pid']})")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    # Prozess ist nicht mehr verfügbar oder Zugriff verweigert
                    continue
                except Exception as e:
                    self.logger.warning(f"Fehler beim Verarbeiten von Prozess: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Fehler beim Durchsuchen der Prozesse: {e}")
            
        return found_processes
    
    def _is_cod_process(self, process_name: str) -> bool:
        """
        Prüft ob ein Prozessname zu einem COD-Spiel gehört
        
        Args:
            process_name (str): Name des Prozesses (lowercase)
            
        Returns:
            bool: True wenn es ein COD-Prozess ist
        """
        if not process_name:
            return False
            
        # Exakte Übereinstimmung mit bekannten COD-Executables
        if process_name in self.cod_executables:
            return True
            
        # Zusätzliche Heuristiken für COD-Erkennung
        cod_keywords = [
            "callofduty",
            "modernwarfare", 
            "blackops",
            "warzone",
            "vanguard",
            "coldwar"
        ]
        
        for keyword in cod_keywords:
            if keyword in process_name:
                return True
                
        return False
    
    def get_process_details(self, pid: int) -> Optional[Dict[str, any]]:
        """
        Holt detaillierte Informationen über einen Prozess
        
        Args:
            pid (int): Prozess-ID
            
        Returns:
            Optional[Dict]: Prozess-Details oder None wenn nicht gefunden
        """
        try:
            process = psutil.Process(pid)
            
            # Erweiterte Prozess-Informationen sammeln
            details = {
                'pid': process.pid,
                'name': process.name(),
                'exe_path': process.exe(),
                'cwd': process.cwd(),
                'cmdline': process.cmdline(),
                'create_time': process.create_time(),
                'memory_info': process.memory_info(),
                'cpu_percent': process.cpu_percent(),
                'status': process.status(),
                'username': process.username() if hasattr(process, 'username') else "Unbekannt"
            }
            
            return details
            
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self.logger.warning(f"Kann nicht auf Prozess {pid} zugreifen: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Fehler beim Abrufen der Prozess-Details für PID {pid}: {e}")
            return None
    
    def is_process_running(self, pid: int) -> bool:
        """
        Prüft ob ein Prozess noch läuft
        
        Args:
            pid (int): Prozess-ID
            
        Returns:
            bool: True wenn der Prozess läuft
        """
        try:
            process = psutil.Process(pid)
            return process.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
        except Exception as e:
            self.logger.warning(f"Fehler beim Prüfen des Prozess-Status für PID {pid}: {e}")
            return False
    
    def get_window_info(self, pid: int) -> Optional[Dict[str, any]]:
        """
        Versucht Fenster-Informationen für einen Prozess zu bekommen
        (Erweiterte Funktionalität für zukünftige Screen-Interaktion)
        
        Args:
            pid (int): Prozess-ID
            
        Returns:
            Optional[Dict]: Fenster-Informationen oder None
        """
        try:
            import pygetwindow as gw
            
            # Alle Fenster durchsuchen
            windows = gw.getAllWindows()
            
            for window in windows:
                # Hier könnte man versuchen, das Fenster dem Prozess zuzuordnen
                # Das ist plattformspezifisch und kann komplex sein
                pass
                
            # Placeholder für zukünftige Implementierung
            return None
            
        except ImportError:
            self.logger.warning("pygetwindow nicht verfügbar für Fenster-Informationen")
            return None
        except Exception as e:
            self.logger.error(f"Fehler beim Abrufen der Fenster-Informationen: {e}")
            return None
    
    def wait_for_cod_process(self, timeout: int = 60) -> Optional[Dict[str, any]]:
        """
        Wartet auf das Erscheinen eines COD-Prozesses
        
        Args:
            timeout (int): Timeout in Sekunden
            
        Returns:
            Optional[Dict]: Erster gefundener COD-Prozess oder None bei Timeout
        """
        import time
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            cod_processes = self.find_cod_processes()
            
            if cod_processes:
                self.logger.info(f"COD-Prozess nach {time.time() - start_time:.1f}s gefunden")
                return cod_processes[0]
                
            time.sleep(1)  # 1 Sekunde warten
            
        self.logger.warning(f"Kein COD-Prozess nach {timeout}s Wartezeit gefunden")
        return None
