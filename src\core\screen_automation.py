"""
Screen-Automatisierung für AutoBot
Grundlagen für Bildschirm-Interaktion und Automatisierung
"""

import pyautogui
import cv2
import numpy as np
import time
from typing import Tuple, Optional, List
from PIL import Image, ImageGrab
from ..utils.logger import Logger

# PyAutoGUI Sicherheitseinstellungen
pyautogui.FAILSAFE = True  # Maus in Ecke bewegen stoppt Automatisierung
pyautogui.PAUSE = 0.1  # Pause zwischen Aktionen

class ScreenAutomation:
    """Klasse für Bildschirm-Automatisierung und -Interaktion"""
    
    def __init__(self):
        self.logger = Logger()
        self.screen_size = pyautogui.size()
        self.logger.info(f"Bildschirmauflösung erkannt: {self.screen_size}")
    
    def take_screenshot(self, region: Optional[Tuple[int, int, int, int]] = None) -> Image.Image:
        """
        Macht einen Screenshot
        
        Args:
            region: Optional (x, y, width, height) für Teilbereich
            
        Returns:
            PIL.Image: Screenshot
        """
        try:
            if region:
                screenshot = ImageGrab.grab(bbox=region)
            else:
                screenshot = ImageGrab.grab()
            
            return screenshot
            
        except Exception as e:
            self.logger.error(f"Fehler beim Screenshot: {e}")
            raise
    
    def find_image_on_screen(self, template_path: str, confidence: float = 0.8, 
                           region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """
        Sucht ein Bild auf dem Bildschirm
        
        Args:
            template_path: Pfad zum Suchbild
            confidence: Mindest-Übereinstimmung (0.0 - 1.0)
            region: Optional Suchbereich (x, y, width, height)
            
        Returns:
            Optional[Tuple[int, int]]: Position (x, y) oder None wenn nicht gefunden
        """
        try:
            # Screenshot machen
            screenshot = self.take_screenshot(region)
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Template laden
            template = cv2.imread(template_path)
            if template is None:
                self.logger.error(f"Template-Bild nicht gefunden: {template_path}")
                return None
            
            # Template Matching
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                # Position berechnen (Mitte des gefundenen Bereichs)
                template_h, template_w = template.shape[:2]
                center_x = max_loc[0] + template_w // 2
                center_y = max_loc[1] + template_h // 2
                
                # Bei region-basierter Suche Offset hinzufügen
                if region:
                    center_x += region[0]
                    center_y += region[1]
                
                self.logger.info(f"Bild gefunden bei ({center_x}, {center_y}) mit Konfidenz {max_val:.2f}")
                return (center_x, center_y)
            else:
                self.logger.debug(f"Bild nicht gefunden. Beste Übereinstimmung: {max_val:.2f}")
                return None
                
        except Exception as e:
            self.logger.error(f"Fehler bei Bildsuche: {e}")
            return None
    
    def click_at_position(self, x: int, y: int, button: str = 'left', clicks: int = 1, 
                         interval: float = 0.0) -> bool:
        """
        Klickt an einer bestimmten Position
        
        Args:
            x, y: Koordinaten
            button: 'left', 'right', 'middle'
            clicks: Anzahl Klicks
            interval: Pause zwischen Klicks
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            # Prüfen ob Position im Bildschirm liegt
            if not (0 <= x <= self.screen_size.width and 0 <= y <= self.screen_size.height):
                self.logger.warning(f"Position ({x}, {y}) außerhalb des Bildschirms")
                return False
            
            pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)
            self.logger.info(f"Geklickt bei ({x}, {y}) mit {button}-Taste")
            return True
            
        except Exception as e:
            self.logger.error(f"Fehler beim Klicken: {e}")
            return False
    
    def click_image(self, template_path: str, confidence: float = 0.8, 
                   button: str = 'left', region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        Sucht ein Bild und klickt darauf
        
        Args:
            template_path: Pfad zum Suchbild
            confidence: Mindest-Übereinstimmung
            button: Maustaste
            region: Optional Suchbereich
            
        Returns:
            bool: True wenn gefunden und geklickt
        """
        position = self.find_image_on_screen(template_path, confidence, region)
        
        if position:
            return self.click_at_position(position[0], position[1], button)
        else:
            self.logger.warning(f"Bild nicht gefunden zum Klicken: {template_path}")
            return False
    
    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """
        Bewegt die Maus zu einer Position
        
        Args:
            x, y: Zielkoordinaten
            duration: Bewegungsdauer in Sekunden
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            pyautogui.moveTo(x, y, duration=duration)
            self.logger.debug(f"Maus bewegt zu ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"Fehler beim Maus bewegen: {e}")
            return False
    
    def send_key(self, key: str, presses: int = 1, interval: float = 0.0) -> bool:
        """
        Sendet Tastatureingaben
        
        Args:
            key: Taste (z.B. 'space', 'enter', 'w', 'a', 's', 'd')
            presses: Anzahl Tastendrücke
            interval: Pause zwischen Tastendrücken
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            pyautogui.press(key, presses=presses, interval=interval)
            self.logger.debug(f"Taste '{key}' {presses}x gedrückt")
            return True
            
        except Exception as e:
            self.logger.error(f"Fehler beim Tastendruck: {e}")
            return False
    
    def send_key_combination(self, *keys) -> bool:
        """
        Sendet Tastenkombination (z.B. Ctrl+C)
        
        Args:
            *keys: Tasten für Kombination
            
        Returns:
            bool: True wenn erfolgreich
        """
        try:
            pyautogui.hotkey(*keys)
            self.logger.debug(f"Tastenkombination gesendet: {'+'.join(keys)}")
            return True
            
        except Exception as e:
            self.logger.error(f"Fehler bei Tastenkombination: {e}")
            return False
    
    def wait_for_image(self, template_path: str, timeout: int = 10, 
                      confidence: float = 0.8, check_interval: float = 0.5,
                      region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int]]:
        """
        Wartet bis ein Bild auf dem Bildschirm erscheint
        
        Args:
            template_path: Pfad zum Suchbild
            timeout: Maximale Wartezeit in Sekunden
            confidence: Mindest-Übereinstimmung
            check_interval: Prüfintervall in Sekunden
            region: Optional Suchbereich
            
        Returns:
            Optional[Tuple[int, int]]: Position wenn gefunden, None bei Timeout
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            position = self.find_image_on_screen(template_path, confidence, region)
            
            if position:
                self.logger.info(f"Bild nach {time.time() - start_time:.1f}s gefunden")
                return position
            
            time.sleep(check_interval)
        
        self.logger.warning(f"Bild nach {timeout}s nicht gefunden: {template_path}")
        return None
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        Holt die Farbe eines Pixels
        
        Args:
            x, y: Koordinaten
            
        Returns:
            Tuple[int, int, int]: RGB-Werte
        """
        try:
            screenshot = self.take_screenshot()
            pixel_color = screenshot.getpixel((x, y))
            return pixel_color[:3]  # Nur RGB, kein Alpha
            
        except Exception as e:
            self.logger.error(f"Fehler beim Abrufen der Pixelfarbe: {e}")
            return (0, 0, 0)
    
    def is_color_at_position(self, x: int, y: int, expected_color: Tuple[int, int, int], 
                           tolerance: int = 10) -> bool:
        """
        Prüft ob eine bestimmte Farbe an einer Position ist
        
        Args:
            x, y: Koordinaten
            expected_color: Erwartete RGB-Farbe
            tolerance: Toleranz für Farbabweichung
            
        Returns:
            bool: True wenn Farbe übereinstimmt
        """
        actual_color = self.get_pixel_color(x, y)
        
        # Farbdifferenz berechnen
        color_diff = sum(abs(a - e) for a, e in zip(actual_color, expected_color))
        
        return color_diff <= tolerance * 3  # 3 Kanäle (RGB)
