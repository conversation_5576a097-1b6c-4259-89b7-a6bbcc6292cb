# AutoBot - COD Process & Text Scanner

Ein Python-basierter AutoBot mit CustomTkinter GUI, der COD-Prozesse überwacht und automatisch auf Bildschirm-Texte wie "Call of Duty" und "Warzone" klickt.

## 🎯 Hauptfunktionen

- **🎮 Prozess-Monitoring**: Automatische Erkennung von COD-<PERSON><PERSON><PERSON>p<PERSON><PERSON>sen (cod.exe und Varianten)
- **🔍 Text-Scanner**: OCR-basierte Erkennung von COD-Texten auf dem Bildschirm
- **🖱️ Auto-Clicker**: Automatisches Klicken auf erkannte Texte ("Call of Duty", "Warzone", etc.)
- **🎨 CustomTkinter GUI**: Moderne, dunkle Benutzeroberfläche
- **⚡ Echtzeit-Überwachung**: Kontinuierliche Überwachung mit Live-Status-Updates
- **📝 Detailliertes Logging**: Vollständiges Aktivitätsprotokoll mit Emojis

## Installation

1. **Repository klonen oder herunterladen**
   ```bash
   git clone <repository-url>
   cd autobot
   ```

2. **Abhängigkeiten installieren**
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Schnellstart

1. **AutoBot starten**
   ```bash
   python main.py
   ```

2. **COD-Spiel starten**
   - Starten Sie Ihr Call of Duty Spiel
   - Der AutoBot erkennt automatisch verschiedene COD-Versionen

3. **Prozess-Monitor aktivieren**
   - Klicken Sie auf "Monitor starten" in der GUI
   - Status zeigt: "COD.exe: Gefunden (PID: XXXX)"

4. **Text-Scanner verwenden**
   - **"Text-Scanner starten"**: Kontinuierliche Suche nach COD-Texten
   - **"Einmaliger Scan"**: Sofortige Suche und Klick
   - **"Warzone suchen"**: Spezifische Warzone-Suche

## 🎯 Automatische Text-Erkennung

Der AutoBot scannt nach folgenden Texten und klickt automatisch darauf:
- **"Call of Duty"** - Hauptmenü-Titel
- **"Warzone"** - Warzone-Modus
- **"Play", "Continue"** - Spielen/Fortsetzen-Buttons
- **"Multiplayer", "Campaign", "Zombies"** - Spiel-Modi
- **"Battle Pass", "Store"** - Menü-Elemente

## Unterstützte COD-Spiele

Der AutoBot erkennt folgende Call of Duty Executables:
- cod.exe (Allgemein)
- codmw.exe, codmw2.exe, codmw3.exe (Modern Warfare Serie)
- codbo.exe, codbo2.exe, codbo3.exe, codbo4.exe (Black Ops Serie)
- codcw.exe (Cold War)
- codvanguard.exe (Vanguard)
- codwarzone.exe, warzone.exe (Warzone)
- Und weitere Varianten

## Projektstruktur

```
autobot/
├── main.py                 # Haupteinstiegspunkt
├── requirements.txt        # Python-Abhängigkeiten
├── README.md              # Diese Datei
├── src/
│   ├── __init__.py
│   ├── gui/
│   │   ├── __init__.py
│   │   └── main_window.py  # CustomTkinter GUI
│   ├── core/
│   │   ├── __init__.py
│   │   ├── process_monitor.py      # Prozess-Überwachung
│   │   └── screen_automation.py    # Screen-Automatisierung
│   └── utils/
│       ├── __init__.py
│       └── logger.py       # Logging-Utility
```

## GUI-Funktionen

- **Status-Anzeige**: Zeigt aktuellen COD-Prozess-Status
- **Monitor-Kontrolle**: Start/Stop der Prozess-Überwachung
- **Manueller Scan**: Einmalige Suche nach COD-Prozessen
- **Aktivitätslog**: Detailliertes Protokoll aller Aktivitäten
- **Log-Verwaltung**: Log löschen und verwalten

## Erweiterte Funktionen (Vorbereitet)

Die Screen-Automatisierung bietet Grundlagen für:
- Bildschirm-Screenshots
- Bilderkennung auf dem Bildschirm
- Automatische Maus-Klicks
- Tastatur-Eingaben
- Pixel-Farb-Erkennung
- Warten auf bestimmte Bildschirm-Inhalte

## Sicherheit

- **PyAutoGUI Failsafe**: Maus in die obere linke Ecke bewegen stoppt alle Automatisierung
- **Prozess-Überwachung**: Nur Lese-Zugriff auf Prozess-Informationen
- **Logging**: Alle Aktivitäten werden protokolliert

## Systemanforderungen

- Python 3.7+
- Windows (getestet), Linux/macOS (sollte funktionieren)
- Ausreichende Berechtigungen für Prozess-Überwachung

## Abhängigkeiten

- `customtkinter>=5.2.0` - Moderne GUI-Bibliothek
- `psutil>=5.9.0` - Prozess-Überwachung
- `pyautogui>=0.9.54` - Screen-Automatisierung
- `Pillow>=10.0.0` - Bildverarbeitung
- `opencv-python>=4.8.0` - Computer Vision
- `numpy>=1.24.0` - Numerische Operationen

## Entwicklung

Für Entwickler, die den AutoBot erweitern möchten:

1. **Neue COD-Spiele hinzufügen**: Erweitern Sie die `cod_executables` Liste in `process_monitor.py`
2. **GUI erweitern**: Modifizieren Sie `main_window.py` für neue Features
3. **Automatisierung hinzufügen**: Nutzen Sie `screen_automation.py` für neue Bildschirm-Interaktionen

## Lizenz

Dieses Projekt ist für Bildungszwecke und persönliche Nutzung gedacht. Bitte beachten Sie die Nutzungsbedingungen der jeweiligen Spiele.

## Haftungsausschluss

Dieser Bot ist für Lern- und Automatisierungszwecke entwickelt. Die Nutzung in Online-Spielen kann gegen die Nutzungsbedingungen verstoßen. Verwenden Sie ihn verantwortungsvoll.
