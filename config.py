"""
AutoBot Konfiguration
"""

# Text-Scanner Einstellungen
TEXT_SCANNER_CONFIG = {
    # Scan-Intervalle
    "scan_interval": 3.0,           # Sekunden zwischen Scans
    "click_delay": 1.0,             # Verzögerung zwischen Klicks
    "max_attempts": 5,              # Maximale Versuche pro Text
    
    # OCR-Einstellungen
    "confidence_threshold": 0.6,    # Mindest-Konfidenz für Klicks (0.0-1.0)
    "use_tesseract": <PERSON>als<PERSON>,         # <PERSON>eract verwenden (falls installiert)
    "use_easyocr": True,            # EasyOCR verwenden
    
    # Bildvorverarbeitung
    "preprocess_image": True,       # Bild für OCR optimieren
    "enhance_contrast": True,       # Kontrast verbessern
    "reduce_noise": True,           # Rauschen reduzieren
}

# Text-Prioritäten (höhere Zahl = höhere Priorität)
# WARZONE HAT ABSOLUTE PRIORITÄT, DANN PLUNDER!
TEXT_PRIORITIES = {
    "warzone": 100,                 # <PERSON>OLUT<PERSON> HÖCHSTE PRIORITÄT
    "plunder": 95,                  # ZWEITHÖCHSTE PRIORITÄT - Warzone Spielmodus
    "call of duty": 50,
    "play": 40,
    "continue": 40,
    "start": 35,

    # Warzone-spezifische Modi (nach Warzone-Klick)
    "havoc royale": 30,
    "battle royale": 30,
    "battle royale casual": 25,
    "resurgence": 25,
    "resurgence casual": 25,
    "warzone ranked play": 20,
    "warzone bootcamp": 20,
    "private match": 15,

    # Allgemeine Modi
    "multiplayer": 30,
    "campaign": 25,
    "zombies": 25,
    "battle pass": 20,
    "store": 15,
    "settings": 10,
    "home": 5,
    "lobby": 5
}

# Warzone-spezifische Einstellungen
WARZONE_CONFIG = {
    "force_warzone_priority": True,     # Warzone immer bevorzugen
    "ignore_other_when_warzone": True,  # Andere Texte ignorieren wenn Warzone gefunden
    "warzone_confidence_boost": 0.1,    # Warzone-Konfidenz um diesen Wert erhöhen
    "warzone_search_terms": [           # Zusätzliche Suchbegriffe für Warzone
        "warzone",
        "war zone",
        "wz",
        "battle royale"
    ]
}

# Plunder-spezifische Einstellungen
PLUNDER_CONFIG = {
    "force_plunder_after_warzone": True,    # Nach Warzone automatisch Plunder suchen
    "plunder_priority_in_warzone_menu": True,  # Plunder in Warzone-Menü bevorzugen
    "plunder_search_terms": [               # Suchbegriffe für Plunder
        "plunder",
        "plunder mode",
        "cash grab"
    ]
}

# COD-Prozess-Namen (für Prozess-Monitor)
COD_EXECUTABLES = [
    "cod.exe",
    "codmw.exe", 
    "codmw2.exe",
    "codmw3.exe",
    "codbo.exe",
    "codbo2.exe",
    "codbo3.exe",
    "codbo4.exe",
    "codcw.exe",
    "codvanguard.exe",
    "codwarzone.exe",
    "modernwarfare.exe",
    "blackops.exe",
    "warzone.exe",
    "callofduty.exe"
]

# GUI-Einstellungen
GUI_CONFIG = {
    "window_title": "AutoBot - COD Process & Text Scanner",
    "window_size": "900x700",
    "theme": "dark",
    "color_theme": "blue",
    "log_max_lines": 1000,          # Maximale Log-Zeilen
    "auto_scroll_log": True,        # Automatisch zum Ende scrollen
    "show_emojis": True,            # Emojis in Log-Nachrichten
}

# Sicherheitseinstellungen
SAFETY_CONFIG = {
    "failsafe_enabled": True,       # PyAutoGUI Failsafe aktivieren
    "require_cod_process": True,    # Nur klicken wenn COD-Prozess läuft
    "max_clicks_per_minute": 30,    # Maximale Klicks pro Minute
    "pause_between_actions": 0.1,   # Pause zwischen Aktionen
}

# Debug-Einstellungen
DEBUG_CONFIG = {
    "log_level": "INFO",            # DEBUG, INFO, WARNING, ERROR
    "save_screenshots": False,      # Screenshots bei Fehlern speichern
    "log_ocr_results": True,        # OCR-Ergebnisse loggen
    "verbose_clicking": True,       # Detaillierte Klick-Logs
}
