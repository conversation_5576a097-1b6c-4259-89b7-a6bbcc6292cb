"""
Auto-Clicker für automatisches Klicken auf erkannte Texte
"""

import time
from typing import List, Dict, Tuple, Optional, Callable
from .text_scanner import TextScanner
from .screen_automation import ScreenAutomation
from ..utils.logger import Logger

# Konfiguration importieren
try:
    from config import TEXT_PRIORITIES, WARZONE_CONFIG, TEXT_SCANNER_CONFIG
except ImportError:
    # Fallback-Konfiguration
    TEXT_PRIORITIES = {"warzone": 100, "call of duty": 50, "play": 40}
    WARZONE_CONFIG = {"force_warzone_priority": True, "ignore_other_when_warzone": True}
    TEXT_SCANNER_CONFIG = {"click_delay": 1.0, "scan_interval": 3.0, "confidence_threshold": 0.6}

class AutoClicker:
    """Klasse für automatisches Klicken auf erkannte Texte"""
    
    def __init__(self):
        self.logger = Logger()
        self.text_scanner = TextScanner()
        self.screen_automation = ScreenAutomation()
        
        # Konfiguration aus config.py laden
        self.click_delay = TEXT_SCANNER_CONFIG.get("click_delay", 1.0)
        self.scan_interval = TEXT_SCANNER_CONFIG.get("scan_interval", 3.0)
        self.max_attempts = TEXT_SCANNER_CONFIG.get("max_attempts", 5)
        self.confidence_threshold = TEXT_SCANNER_CONFIG.get("confidence_threshold", 0.6)
        
        # Prioritätsliste für COD-Texte (aus config.py)
        self.text_priorities = TEXT_PRIORITIES

        # Warzone-spezifische Konfiguration
        self.force_warzone_priority = WARZONE_CONFIG.get("force_warzone_priority", True)
        self.ignore_other_when_warzone = WARZONE_CONFIG.get("ignore_other_when_warzone", True)

        self.logger.info(f"🎯 AutoClicker initialisiert - Warzone hat absolute Priorität: {self.force_warzone_priority}")
        
        # Callback-Funktionen für Events
        self.on_text_found: Optional[Callable] = None
        self.on_click_performed: Optional[Callable] = None
        self.on_scan_complete: Optional[Callable] = None
    
    def set_callbacks(self, on_text_found=None, on_click_performed=None, on_scan_complete=None):
        """
        Setzt Callback-Funktionen für Events
        
        Args:
            on_text_found: Callback wenn Text gefunden wird
            on_click_performed: Callback wenn Klick ausgeführt wird
            on_scan_complete: Callback wenn Scan abgeschlossen ist
        """
        self.on_text_found = on_text_found
        self.on_click_performed = on_click_performed
        self.on_scan_complete = on_scan_complete
    
    def click_on_text(self, target_text: str, region: Optional[Tuple[int, int, int, int]] = None, 
                     max_attempts: int = None) -> bool:
        """
        Sucht nach einem Text und klickt darauf
        
        Args:
            target_text: Zu suchender Text
            region: Optional Suchbereich (x, y, width, height)
            max_attempts: Maximale Versuche (None = Standard verwenden)
            
        Returns:
            bool: True wenn erfolgreich geklickt
        """
        attempts = max_attempts or self.max_attempts
        
        for attempt in range(attempts):
            try:
                self.logger.info(f"Suche nach Text '{target_text}' (Versuch {attempt + 1}/{attempts})")
                
                # Nach Text suchen
                position = self.text_scanner.scan_for_clickable_text(target_text, region)
                
                if position:
                    x, y = position
                    
                    # Callback aufrufen
                    if self.on_text_found:
                        self.on_text_found(target_text, position)
                    
                    # Klicken
                    success = self.screen_automation.click_at_position(x, y)
                    
                    if success:
                        self.logger.info(f"Erfolgreich auf '{target_text}' bei ({x}, {y}) geklickt")
                        
                        # Callback aufrufen
                        if self.on_click_performed:
                            self.on_click_performed(target_text, position)
                        
                        time.sleep(self.click_delay)
                        return True
                    else:
                        self.logger.warning(f"Klick auf '{target_text}' fehlgeschlagen")
                else:
                    self.logger.debug(f"Text '{target_text}' nicht gefunden (Versuch {attempt + 1})")
                
                # Warten vor nächstem Versuch
                if attempt < attempts - 1:
                    time.sleep(self.scan_interval)
                    
            except Exception as e:
                self.logger.error(f"Fehler beim Klicken auf '{target_text}': {e}")
        
        self.logger.warning(f"Text '{target_text}' nach {attempts} Versuchen nicht gefunden")
        return False
    
    def click_on_multiple_texts(self, target_texts: List[str], 
                               region: Optional[Tuple[int, int, int, int]] = None,
                               stop_on_first_success: bool = True) -> Dict[str, bool]:
        """
        Sucht nach mehreren Texten und klickt auf sie
        
        Args:
            target_texts: Liste der zu suchenden Texte
            region: Optional Suchbereich
            stop_on_first_success: Stoppen nach erstem erfolgreichen Klick
            
        Returns:
            Dict[str, bool]: Ergebnisse für jeden Text
        """
        results = {}
        
        # Texte nach Priorität sortieren
        sorted_texts = sorted(target_texts, 
                            key=lambda x: self.text_priorities.get(x.lower(), 0), 
                            reverse=True)
        
        for text in sorted_texts:
            self.logger.info(f"Versuche Klick auf: {text}")
            success = self.click_on_text(text, region)
            results[text] = success
            
            if success and stop_on_first_success:
                self.logger.info(f"Erfolgreich geklickt auf '{text}', stoppe weitere Versuche")
                break
        
        return results
    
    def scan_and_click_cod_elements(self, region: Optional[Tuple[int, int, int, int]] = None) -> List[Dict[str, any]]:
        """
        Scannt nach COD-Menü-Elementen und klickt auf sie
        
        Args:
            region: Optional Suchbereich
            
        Returns:
            List[Dict]: Liste der gefundenen und geklickten Elemente
        """
        try:
            self.logger.info("Starte Scan nach COD-Menü-Elementen...")
            
            # Nach COD-Menü-Elementen suchen
            found_elements = self.text_scanner.find_cod_menu_elements(region)
            
            if not found_elements:
                self.logger.info("Keine COD-Menü-Elemente gefunden")
                return []
            
            # WARZONE HAT ABSOLUTE PRIORITÄT!
            warzone_elements = [elem for elem in found_elements if elem['target'] == 'warzone']

            if warzone_elements and self.force_warzone_priority:
                # Nur Warzone-Elemente verwenden, sortiert nach Konfidenz
                sorted_elements = sorted(warzone_elements, key=lambda x: x['confidence'], reverse=True)
                self.logger.info(f"🎯 WARZONE GEFUNDEN! Ignoriere alle anderen Texte und klicke NUR auf Warzone!")
                self.logger.info(f"🎯 Gefundene Warzone-Elemente: {len(warzone_elements)}")
            else:
                # Nach Priorität und Konfidenz sortieren (aber Warzone bleibt höchste Priorität)
                sorted_elements = sorted(found_elements,
                                       key=lambda x: (
                                           self.text_priorities.get(x['target'], 0),
                                           x['confidence']
                                       ), reverse=True)

                if not warzone_elements:
                    self.logger.info(f"ℹ️ Kein Warzone gefunden. Verwende andere Texte: {[elem['target'] for elem in found_elements[:3]]}")
            
            clicked_elements = []
            
            for element in sorted_elements:
                target = element['target']
                position = element['position']
                confidence = element['confidence']
                
                # Nur klicken wenn Konfidenz hoch genug
                if confidence >= self.confidence_threshold:
                    self.logger.info(f"Klicke auf '{target}' (Konfidenz: {confidence:.2f})")
                    
                    # Callback aufrufen
                    if self.on_text_found:
                        self.on_text_found(target, position)
                    
                    # Klicken
                    success = self.screen_automation.click_at_position(position[0], position[1])
                    
                    if success:
                        element['clicked'] = True
                        clicked_elements.append(element)
                        
                        # Callback aufrufen
                        if self.on_click_performed:
                            self.on_click_performed(target, position)
                        
                        self.logger.info(f"Erfolgreich auf '{target}' geklickt")
                        time.sleep(self.click_delay)
                        
                        # Nur auf das erste Element klicken
                        break
                    else:
                        self.logger.warning(f"Klick auf '{target}' fehlgeschlagen")
                        element['clicked'] = False
                else:
                    self.logger.debug(f"Überspringe '{target}' (Konfidenz zu niedrig: {confidence:.2f})")
            
            # Callback aufrufen
            if self.on_scan_complete:
                self.on_scan_complete(found_elements, clicked_elements)
            
            return clicked_elements
            
        except Exception as e:
            self.logger.error(f"Fehler beim Scannen und Klicken: {e}")
            return []
    
    def wait_and_click_when_appears(self, target_text: str, timeout: int = 30,
                                   region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        Wartet bis ein Text erscheint und klickt dann darauf
        
        Args:
            target_text: Zu suchender Text
            timeout: Maximale Wartezeit in Sekunden
            region: Optional Suchbereich
            
        Returns:
            bool: True wenn erfolgreich geklickt
        """
        start_time = time.time()
        
        self.logger.info(f"Warte auf Text '{target_text}' (Timeout: {timeout}s)")
        
        while time.time() - start_time < timeout:
            try:
                position = self.text_scanner.scan_for_clickable_text(target_text, region)
                
                if position:
                    self.logger.info(f"Text '{target_text}' erschienen nach {time.time() - start_time:.1f}s")
                    
                    # Callback aufrufen
                    if self.on_text_found:
                        self.on_text_found(target_text, position)
                    
                    # Klicken
                    success = self.screen_automation.click_at_position(position[0], position[1])
                    
                    if success:
                        # Callback aufrufen
                        if self.on_click_performed:
                            self.on_click_performed(target_text, position)
                        
                        return True
                
                time.sleep(self.scan_interval)
                
            except Exception as e:
                self.logger.error(f"Fehler beim Warten auf Text: {e}")
                time.sleep(1)
        
        self.logger.warning(f"Text '{target_text}' ist nach {timeout}s nicht erschienen")
        return False
    
    def continuous_scan_and_click(self, target_texts: List[str], 
                                 duration: int = 60,
                                 region: Optional[Tuple[int, int, int, int]] = None) -> Dict[str, int]:
        """
        Kontinuierliches Scannen und Klicken für eine bestimmte Dauer
        
        Args:
            target_texts: Liste der zu suchenden Texte
            duration: Dauer in Sekunden
            region: Optional Suchbereich
            
        Returns:
            Dict[str, int]: Anzahl der Klicks pro Text
        """
        start_time = time.time()
        click_counts = {text: 0 for text in target_texts}
        
        self.logger.info(f"Starte kontinuierliches Scannen für {duration}s")
        
        while time.time() - start_time < duration:
            try:
                for text in target_texts:
                    position = self.text_scanner.scan_for_clickable_text(text, region)
                    
                    if position:
                        success = self.screen_automation.click_at_position(position[0], position[1])
                        
                        if success:
                            click_counts[text] += 1
                            self.logger.info(f"Klick #{click_counts[text]} auf '{text}'")
                            
                            # Callback aufrufen
                            if self.on_click_performed:
                                self.on_click_performed(text, position)
                            
                            time.sleep(self.click_delay)
                
                time.sleep(self.scan_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Kontinuierliches Scannen durch Benutzer unterbrochen")
                break
            except Exception as e:
                self.logger.error(f"Fehler beim kontinuierlichen Scannen: {e}")
                time.sleep(1)
        
        total_clicks = sum(click_counts.values())
        self.logger.info(f"Kontinuierliches Scannen beendet. Gesamt-Klicks: {total_clicks}")
        
        return click_counts
