"""
Logger-Utility für AutoBot
"""

import logging
import os
from datetime import datetime
from typing import Optional

class Logger:
    """Einfache Logger-Klasse für AutoBot"""
    
    def __init__(self, name: str = "AutoBot", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        
        # Verhindere doppelte Handler
        if not self.logger.handlers:
            self.logger.setLevel(logging.DEBUG)
            
            # Formatter erstellen
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # Console Handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            
            # File Handler (optional)
            if log_file:
                # Logs-Verzeichnis erstellen falls nicht vorhanden
                log_dir = os.path.dirname(log_file)
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
    
    def debug(self, message: str):
        """Debug-Nachricht loggen"""
        self.logger.debug(message)
    
    def info(self, message: str):
        """Info-Nachricht loggen"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """Warning-Nachricht loggen"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Error-Nachricht loggen"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """Critical-Nachricht loggen"""
        self.logger.critical(message)
